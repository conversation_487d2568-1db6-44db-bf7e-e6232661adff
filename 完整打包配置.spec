# -*- mode: python ; coding: utf-8 -*-
"""
完整打包配置 - 将登录注册.py作为主入口，包含股票看图软件的所有功能
打包成单个exe文件，包含所有依赖模块和资源文件
"""

import os

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(SPEC))

# 需要包含的所有Python模块文件
python_modules = [
    ('股票看图软件_增强版.py', '.'),
    ('回测系统.py', '.'),
    ('回测分析.py', '.'),
    ('策略模板.py', '.'),
    ('多股票回测系统.py', '.'),
    ('技术指标库.py', '.'),
    ('市场数据管理.py', '.'),
    ('使用者监控.py', '.'),
    ('交易调度器.py', '.'),
    ('多股票监控管理器.py', '.'),
    ('多股票监控配置.py', '.'),
    ('浏览器驱动管理.py', '.'),
    ('网页交易买入.py', '.'),
    ('缠论优化策略.py', '.'),
    ('缠论策略模板.py', '.'),
    ('缠论高收益策略.py', '.'),
    ('缠论高收益策略_独立版.py', '.'),
]

# 需要包含的目录
directories = [
    ('策略示例', '策略示例'),
    ('user_config', 'user_config'),
    ('drivers', 'drivers'),
]

# 需要包含的配置和数据文件
config_files = [
    ('browser_config.json', '.'),
    ('multi_stock_config.json', '.'),
    ('test_config.json', '.'),
    ('tushare_token.txt', '.'),
]

# 合并所有数据文件
all_data_files = python_modules + directories + config_files

# 所有需要的隐藏导入
hidden_imports = [
    # 基础GUI库
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.scrolledtext',
    'tkinter.filedialog',
    
    # 数据处理库
    'pandas',
    'numpy',
    'tushare',
    
    # 图表库
    'matplotlib',
    'matplotlib.pyplot',
    'matplotlib.backends.backend_tkagg',
    'matplotlib.figure',
    'matplotlib.backends.backend_tkagg',
    
    # 网页自动化
    'selenium',
    'selenium.webdriver',
    'selenium.webdriver.edge.service',
    'selenium.webdriver.edge.options',
    'selenium.webdriver.chrome.service',
    'selenium.webdriver.chrome.options',
    'selenium.webdriver.common.by',
    'selenium.webdriver.support.ui',
    'selenium.webdriver.support',
    'selenium.webdriver.support.expected_conditions',
    'webdriver_manager',
    'webdriver_manager.microsoft',
    'webdriver_manager.chrome',
    
    # 图像处理
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    
    # 网络请求
    'requests',
    'urllib3',
    
    # Excel处理
    'openpyxl',
    'xlrd',
    'xlsxwriter',
    
    # 系统和进程
    'psutil',
    'subprocess',
    'threading',
    'multiprocessing',
    
    # 数据格式
    'json',
    'pickle',
    'gzip',
    'base64',
    
    # 时间处理
    'datetime',
    'time',
    
    # 数学计算
    'math',
    'statistics',
    
    # 文件处理
    'os',
    'sys',
    'io',
    'tempfile',
    'pathlib',
    
    # 网页解析
    'lxml',
    'bs4',
    'html.parser',
    
    # 加密和哈希
    'hashlib',
    'hmac',
    
    # 正则表达式
    're',
    
    # 类型提示
    'typing',
    
    # 抽象基类
    'abc',
    
    # 警告处理
    'warnings',
    
    # 动态导入
    'importlib',
    'importlib.util',
    
    # 平台检测
    'platform',
    
    # 随机数
    'random',
    
    # 集合类型
    'collections',
    
    # 迭代工具
    'itertools',
    
    # 函数工具
    'functools',
    
    # 复制工具
    'copy',
    
    # 压缩
    'zipfile',
    'tarfile',
    
    # 网络
    'socket',
    'http.client',
    'urllib.request',
    'urllib.parse',
    
    # 编码
    'codecs',
    'encodings',
    'encodings.utf_8',
    'encodings.gbk',
    'encodings.cp936',
]

# 需要排除的大型库（减少exe大小）
excludes = [
    'PyQt5',
    'PyQt6', 
    'PySide2',
    'PySide6',
    'torch',
    'tensorflow',
    'cv2',
    'sklearn',
    'scipy.sparse.csgraph._validation',
    'IPython',
    'jupyter',
    'notebook',
    'matplotlib.tests',
    'pandas.tests',
    'numpy.tests',
    'test',
    'tests',
    'unittest',
    'doctest',
]

a = Analysis(
    ['登录注册.py'],  # 主入口文件
    pathex=[current_dir],
    binaries=[],
    datas=all_data_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='股票交易系统',  # exe文件名
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # 启用UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
