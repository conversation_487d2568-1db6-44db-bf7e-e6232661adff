# -*- mode: python ; coding: utf-8 -*-
"""
股票交易系统完整打包配置
主入口：登录注册.py
包含：股票看图软件_增强版.py 及所有依赖模块
目标：单个exe文件，包含所有功能
"""

import os
import sys

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(SPEC))

# ==================== 核心Python模块文件 ====================
core_modules = [
    # 主要功能模块
    ('股票看图软件_增强版.py', '.'),
    ('回测系统.py', '.'),
    ('回测分析.py', '.'),
    ('策略模板.py', '.'),
    ('多股票回测系统.py', '.'),
    ('技术指标库.py', '.'),
    ('市场数据管理.py', '.'),
    ('使用者监控.py', '.'),
    
    # 交易相关模块
    ('交易调度器.py', '.'),
    ('多股票监控管理器.py', '.'),
    ('多股票监控配置.py', '.'),
    ('网页交易买入.py', '.'),
    
    # 浏览器和驱动管理
    ('浏览器驱动管理.py', '.'),
    ('安装浏览器驱动.py', '.'),
    
    # 缠论策略模块
    ('缠论优化策略.py', '.'),
    ('缠论策略模板.py', '.'),
    ('缠论高收益策略.py', '.'),
    ('缠论高收益策略_独立版.py', '.'),
]

# ==================== 目录和数据文件 ====================
directories_and_files = [
    # 策略示例目录
    ('策略示例', '策略示例'),
    
    # 配置目录
    ('user_config', 'user_config'),
    
    # 浏览器驱动目录（如果存在）
    ('drivers', 'drivers'),
    
    # 配置文件
    ('browser_config.json', '.'),
    ('multi_stock_config.json', '.'),
    ('test_config.json', '.'),
    ('tushare_token.txt', '.'),
    
    # 说明文档
    ('使用者监控系统说明.md', '.'),
    ('多股票监控使用说明.md', '.'),
    ('新功能说明.md', '.'),
    ('浏览器优化说明.md', '.'),
    ('浏览器连接改进说明.md', '.'),
    ('浏览器驱动说明.md', '.'),
    ('登录成功提示说明.md', '.'),
    ('登录界面状态提示说明.md', '.'),
    ('自定义策略收集功能说明.md', '.'),
    ('进程清理修复说明.md', '.'),
    ('缓存优化效果对比.md', '.'),
    
    # 源代码文件
    ('上传网址源代码.txt', '.'),
    ('缠论策略代码.txt', '.'),
]

# 合并所有数据文件
all_data_files = core_modules + directories_and_files

# ==================== 隐藏导入模块 ====================
hidden_imports = [
    # ===== 基础GUI和系统库 =====
    'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.scrolledtext', 
    'tkinter.filedialog', 'tkinter.simpledialog',
    
    # ===== 数据处理核心库 =====
    'pandas', 'numpy', 'tushare',
    'pandas.core', 'pandas.core.arrays', 'pandas.core.dtypes',
    'pandas.io', 'pandas.io.formats', 'pandas.io.common',
    'numpy.core', 'numpy.lib', 'numpy.random',
    
    # ===== 图表和可视化 =====
    'matplotlib', 'matplotlib.pyplot', 'matplotlib.figure',
    'matplotlib.backends', 'matplotlib.backends.backend_tkagg',
    'matplotlib.backends.backend_agg', 'matplotlib.font_manager',
    'matplotlib.dates', 'matplotlib.ticker',
    
    # ===== 网页自动化完整套件 =====
    'selenium', 'selenium.webdriver', 'selenium.webdriver.common',
    'selenium.webdriver.common.by', 'selenium.webdriver.common.keys',
    'selenium.webdriver.common.action_chains', 'selenium.webdriver.common.desired_capabilities',
    'selenium.webdriver.support', 'selenium.webdriver.support.ui',
    'selenium.webdriver.support.expected_conditions', 'selenium.webdriver.support.wait',
    
    # Edge浏览器支持
    'selenium.webdriver.edge', 'selenium.webdriver.edge.service',
    'selenium.webdriver.edge.options', 'selenium.webdriver.edge.webdriver',
    
    # Chrome浏览器支持
    'selenium.webdriver.chrome', 'selenium.webdriver.chrome.service',
    'selenium.webdriver.chrome.options', 'selenium.webdriver.chrome.webdriver',
    
    # WebDriver管理器
    'webdriver_manager', 'webdriver_manager.microsoft', 'webdriver_manager.chrome',
    'webdriver_manager.core', 'webdriver_manager.utils',
    
    # ===== 图像处理 =====
    'PIL', 'PIL.Image', 'PIL.ImageTk', 'PIL.ImageDraw', 'PIL.ImageFont',
    
    # ===== 网络和HTTP =====
    'requests', 'requests.adapters', 'requests.auth', 'requests.cookies',
    'urllib3', 'urllib3.util', 'urllib3.poolmanager',
    'http.client', 'urllib.request', 'urllib.parse', 'urllib.error',
    
    # ===== Excel和数据文件处理 =====
    'openpyxl', 'openpyxl.workbook', 'openpyxl.worksheet', 'openpyxl.styles',
    'xlrd', 'xlsxwriter', 'xlwt',
    
    # ===== 系统和进程管理 =====
    'psutil', 'subprocess', 'threading', 'multiprocessing',
    'multiprocessing.pool', 'concurrent.futures',
    
    # ===== 数据格式和序列化 =====
    'json', 'pickle', 'gzip', 'base64', 'csv',
    'xml.etree.ElementTree', 'xml.dom.minidom',
    
    # ===== 时间和日期 =====
    'datetime', 'time', 'calendar', 'dateutil', 'dateutil.parser',
    
    # ===== 数学和统计 =====
    'math', 'statistics', 'random', 'decimal', 'fractions',
    
    # ===== 文件和路径处理 =====
    'os', 'sys', 'io', 'tempfile', 'pathlib', 'shutil', 'glob',
    
    # ===== 网页解析 =====
    'lxml', 'lxml.etree', 'lxml.html',
    'bs4', 'bs4.element', 'html.parser',
    
    # ===== 加密和安全 =====
    'hashlib', 'hmac', 'ssl', 'secrets',
    
    # ===== 正则表达式和文本处理 =====
    're', 'string', 'textwrap', 'unicodedata',
    
    # ===== 类型系统和反射 =====
    'typing', 'types', 'inspect', 'abc',
    
    # ===== 动态导入和模块管理 =====
    'importlib', 'importlib.util', 'importlib.machinery',
    'pkgutil', 'modulefinder',
    
    # ===== 平台和环境 =====
    'platform', 'locale', 'getpass',
    
    # ===== 集合和数据结构 =====
    'collections', 'collections.abc', 'heapq', 'bisect',
    
    # ===== 函数式编程工具 =====
    'itertools', 'functools', 'operator',
    
    # ===== 对象复制和比较 =====
    'copy', 'deepdiff',
    
    # ===== 压缩和归档 =====
    'zipfile', 'tarfile', 'zlib',
    
    # ===== 网络协议 =====
    'socket', 'socketserver', 'ftplib', 'smtplib',
    
    # ===== 编码和解码 =====
    'codecs', 'encodings', 'encodings.utf_8', 'encodings.gbk', 'encodings.cp936',
    'encodings.ascii', 'encodings.latin_1',
    
    # ===== 日志系统 =====
    'logging', 'logging.handlers', 'logging.config',
    
    # ===== 配置文件处理 =====
    'configparser', 'argparse', 'optparse',
    
    # ===== 队列和同步 =====
    'queue', 'threading', 'asyncio',
    
    # ===== 测试框架（可能被某些模块使用） =====
    'unittest', 'unittest.mock',
    
    # ===== 特定于金融数据的模块 =====
    'talib',  # 技术分析库（如果安装了）
]

# ==================== 需要排除的模块 ====================
excludes = [
    # 大型GUI框架
    'PyQt5', 'PyQt6', 'PySide2', 'PySide6', 'wx',
    
    # 机器学习和深度学习框架
    'torch', 'tensorflow', 'keras', 'sklearn', 'scipy.sparse.csgraph._validation',
    'cv2', 'skimage',
    
    # 开发和调试工具
    'IPython', 'jupyter', 'notebook', 'pytest', 'nose',
    
    # 测试模块
    'test', 'tests', 'testing',
    'matplotlib.tests', 'pandas.tests', 'numpy.tests',
    'unittest.test', 'doctest',
    
    # 文档生成
    'sphinx', 'docutils',
    
    # 其他不需要的大型库
    'sympy', 'networkx', 'plotly',
]

# ==================== PyInstaller配置 ====================
a = Analysis(
    ['登录注册.py'],  # 主入口文件
    pathex=[current_dir],
    binaries=[],
    datas=all_data_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 数据文件处理
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 生成单个exe文件
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='股票交易系统',  # exe文件名
    debug=False,  # 不启用调试模式
    bootloader_ignore_signals=False,
    strip=False,  # 不删除符号信息
    upx=True,  # 启用UPX压缩减小文件大小
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口（GUI应用）
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径，如 'icon.ico'
)
