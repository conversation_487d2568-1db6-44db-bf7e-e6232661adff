#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证打包配置完整性脚本
检查所有必要的模块、文件和依赖是否存在
"""

import os
import sys
import importlib
from pathlib import Path

def check_python_modules():
    """检查Python模块是否可以正常导入"""
    print("🔍 检查Python模块导入...")
    
    # 核心模块列表
    core_modules = [
        'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.scrolledtext',
        'pandas', 'numpy', 'matplotlib', 'matplotlib.pyplot',
        'tushare', 'selenium', 'PIL', 'requests', 'openpyxl', 'psutil'
    ]
    
    # 可选模块列表
    optional_modules = [
        'webdriver_manager', 'lxml', 'bs4', 'xlrd', 'xlsxwriter'
    ]
    
    missing_core = []
    missing_optional = []
    
    # 检查核心模块
    for module in core_modules:
        try:
            importlib.import_module(module)
            print(f"  ✅ {module}")
        except ImportError:
            missing_core.append(module)
            print(f"  ❌ {module} - 缺失")
    
    # 检查可选模块
    for module in optional_modules:
        try:
            importlib.import_module(module)
            print(f"  ✅ {module} (可选)")
        except ImportError:
            missing_optional.append(module)
            print(f"  ⚠️ {module} - 缺失 (可选)")
    
    return missing_core, missing_optional

def check_project_files():
    """检查项目文件是否存在"""
    print("\n📁 检查项目文件...")
    
    # 必需的Python文件
    required_files = [
        '登录注册.py',
        '股票看图软件_增强版.py',
        '回测系统.py',
        '回测分析.py',
        '策略模板.py',
        '多股票回测系统.py',
        '技术指标库.py',
        '市场数据管理.py',
        '使用者监控.py',
        '交易调度器.py',
        '多股票监控管理器.py',
        '多股票监控配置.py',
        '网页交易买入.py',
        '浏览器驱动管理.py',
        '缠论优化策略.py',
        '缠论策略模板.py',
        '缠论高收益策略.py',
        '缠论高收益策略_独立版.py',
    ]
    
    # 必需的目录
    required_dirs = [
        '策略示例',
        'user_config',
    ]
    
    # 配置文件（可选）
    config_files = [
        'browser_config.json',
        'multi_stock_config.json',
        'test_config.json',
        'tushare_token.txt',
    ]
    
    missing_files = []
    missing_dirs = []
    missing_configs = []
    
    # 检查Python文件
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            missing_files.append(file)
            print(f"  ❌ {file} - 缺失")
    
    # 检查目录
    for dir_name in required_dirs:
        if os.path.isdir(dir_name):
            print(f"  ✅ {dir_name}/ (目录)")
        else:
            missing_dirs.append(dir_name)
            print(f"  ❌ {dir_name}/ - 缺失 (目录)")
    
    # 检查配置文件
    for file in config_files:
        if os.path.exists(file):
            print(f"  ✅ {file} (配置)")
        else:
            missing_configs.append(file)
            print(f"  ⚠️ {file} - 缺失 (配置，可选)")
    
    return missing_files, missing_dirs, missing_configs

def check_spec_file():
    """检查spec文件是否存在"""
    print("\n📋 检查打包配置文件...")
    
    spec_file = '股票交易系统_完整打包.spec'
    if os.path.exists(spec_file):
        print(f"  ✅ {spec_file}")
        return True
    else:
        print(f"  ❌ {spec_file} - 缺失")
        return False

def check_local_imports():
    """检查本地模块是否可以导入"""
    print("\n🔗 检查本地模块导入...")
    
    local_modules = [
        '股票看图软件_增强版',
        '回测系统',
        '回测分析',
        '策略模板',
        '多股票回测系统',
        '技术指标库',
        '市场数据管理',
        '使用者监控',
    ]
    
    import_errors = []
    
    for module in local_modules:
        try:
            # 尝试导入模块
            importlib.import_module(module)
            print(f"  ✅ {module}")
        except ImportError as e:
            import_errors.append((module, str(e)))
            print(f"  ❌ {module} - 导入失败: {e}")
        except Exception as e:
            import_errors.append((module, str(e)))
            print(f"  ⚠️ {module} - 其他错误: {e}")
    
    return import_errors

def generate_install_commands(missing_modules):
    """生成安装命令"""
    if not missing_modules:
        return None
    
    print("\n💡 缺失模块安装命令:")
    install_cmd = "pip install " + " ".join(missing_modules)
    print(f"  {install_cmd}")
    return install_cmd

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 股票交易系统打包配置验证")
    print("=" * 60)
    
    # 检查Python模块
    missing_core, missing_optional = check_python_modules()
    
    # 检查项目文件
    missing_files, missing_dirs, missing_configs = check_project_files()
    
    # 检查spec文件
    spec_exists = check_spec_file()
    
    # 检查本地模块导入
    import_errors = check_local_imports()
    
    # 生成报告
    print("\n" + "=" * 60)
    print("📊 验证结果汇总")
    print("=" * 60)
    
    # 核心模块检查结果
    if missing_core:
        print(f"❌ 缺失核心模块: {len(missing_core)}个")
        for module in missing_core:
            print(f"   - {module}")
        generate_install_commands(missing_core)
    else:
        print("✅ 所有核心模块检查通过")
    
    # 可选模块检查结果
    if missing_optional:
        print(f"⚠️ 缺失可选模块: {len(missing_optional)}个")
        for module in missing_optional:
            print(f"   - {module}")
        generate_install_commands(missing_optional)
    else:
        print("✅ 所有可选模块检查通过")
    
    # 项目文件检查结果
    if missing_files or missing_dirs:
        print(f"❌ 缺失项目文件: {len(missing_files + missing_dirs)}个")
        for file in missing_files + missing_dirs:
            print(f"   - {file}")
    else:
        print("✅ 所有项目文件检查通过")
    
    # 配置文件检查结果
    if missing_configs:
        print(f"⚠️ 缺失配置文件: {len(missing_configs)}个 (可选)")
        for file in missing_configs:
            print(f"   - {file}")
    
    # spec文件检查结果
    if not spec_exists:
        print("❌ 缺失打包配置文件")
    else:
        print("✅ 打包配置文件检查通过")
    
    # 本地模块导入检查结果
    if import_errors:
        print(f"❌ 本地模块导入错误: {len(import_errors)}个")
        for module, error in import_errors:
            print(f"   - {module}: {error}")
    else:
        print("✅ 所有本地模块导入检查通过")
    
    # 总体结论
    print("\n" + "=" * 60)
    if not missing_core and not missing_files and not missing_dirs and spec_exists and not import_errors:
        print("🎉 验证通过！可以开始打包")
        print("💡 运行命令: 自动打包脚本.bat")
    else:
        print("⚠️ 发现问题，请先解决上述问题后再进行打包")
        if missing_core:
            print("🔧 请先安装缺失的核心模块")
        if missing_files or missing_dirs:
            print("🔧 请确保所有必需的项目文件存在")
        if not spec_exists:
            print("🔧 请确保打包配置文件存在")
        if import_errors:
            print("🔧 请解决本地模块导入问题")
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")
