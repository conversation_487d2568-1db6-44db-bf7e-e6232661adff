@echo off
chcp 65001 >nul
echo ========================================
echo 股票交易系统自动打包脚本
echo ========================================
echo.

:: 检查Python环境
echo [1/6] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python环境，请先安装Python 3.8+
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

:: 检查PyInstaller
echo.
echo [2/6] 检查PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 未找到PyInstaller，正在安装...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ PyInstaller安装失败
        pause
        exit /b 1
    )
)
echo ✅ PyInstaller检查通过

:: 检查必要的依赖包
echo.
echo [3/6] 检查必要依赖包...
echo 正在检查核心依赖...

python -c "import tkinter, pandas, numpy, matplotlib, tushare, selenium, PIL, requests, openpyxl, psutil" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 发现缺失的依赖包，正在安装...
    pip install pandas numpy matplotlib tushare selenium pillow requests openpyxl psutil webdriver-manager lxml beautifulsoup4
    if errorlevel 1 (
        echo ❌ 依赖包安装失败，请手动安装
        echo 需要安装的包：pandas numpy matplotlib tushare selenium pillow requests openpyxl psutil webdriver-manager lxml beautifulsoup4
        pause
        exit /b 1
    )
)
echo ✅ 依赖包检查通过

:: 清理之前的构建文件
echo.
echo [4/6] 清理之前的构建文件...
if exist "build" (
    echo 删除build目录...
    rmdir /s /q "build"
)
if exist "dist" (
    echo 删除dist目录...
    rmdir /s /q "dist"
)
if exist "股票交易系统.exe" (
    echo 删除旧的exe文件...
    del "股票交易系统.exe"
)
echo ✅ 清理完成

:: 开始打包
echo.
echo [5/6] 开始打包...
echo 这可能需要几分钟时间，请耐心等待...
echo.

pyinstaller --clean "股票交易系统_完整打包.spec"

if errorlevel 1 (
    echo.
    echo ❌ 打包失败！
    echo 请检查错误信息并修复问题后重试
    pause
    exit /b 1
)

:: 检查生成的文件
echo.
echo [6/6] 检查生成的文件...
if exist "dist\股票交易系统.exe" (
    echo ✅ 打包成功！
    echo.
    echo 📁 生成的文件位置：
    echo    %cd%\dist\股票交易系统.exe
    echo.
    echo 📊 文件大小：
    for %%A in ("dist\股票交易系统.exe") do echo    %%~zA 字节
    echo.
    echo 🎉 打包完成！您可以将exe文件分发给用户使用。
    echo.
    echo 💡 使用说明：
    echo    1. 双击运行 股票交易系统.exe
    echo    2. 首次运行会显示登录注册界面
    echo    3. 登录成功后自动跳转到股票看图软件
    echo    4. 所有功能都已集成在单个exe文件中
    echo.
) else (
    echo ❌ 打包失败：未找到生成的exe文件
    echo 请检查打包过程中的错误信息
)

echo.
echo 按任意键退出...
pause >nul
